#!/usr/bin/env python3
"""
測試 pproxy 庫的安裝和基本功能
驗證 SOCKS5 到 HTTP 代理轉換是否正常工作
"""

import os
import sys
import time
import subprocess
import urllib.request
from dotenv import load_dotenv

# 載入環境變量
load_dotenv()

def install_pproxy():
    """檢查 pproxy 庫是否可用"""
    try:
        import pproxy
        print("✅ pproxy already installed")
        return True
    except ImportError:
        print("❌ pproxy not found. Please install it using:")
        print("  uv add pproxy")
        print("  or")
        print("  pip install pproxy")
        return False

def test_pproxy_conversion():
    """測試 pproxy SOCKS5 到 HTTP 轉換功能"""
    nordvpn_proxy = os.getenv('NORDVPN_PROXY')
    if not nordvpn_proxy:
        print("❌ NORDVPN_PROXY not found in environment variables")
        return False
    
    print(f"🔗 Testing SOCKS5 proxy: {nordvpn_proxy}")
    
    # 設置本地 HTTP 代理端口
    http_proxy_port = 8889  # 使用不同的端口避免衝突
    
    try:
        # 轉換 SOCKS5 URL 格式為 pproxy 格式
        # 從 socks5://user:pass@host:port 轉換為 socks5://host:port#user:pass
        if "@" in nordvpn_proxy:
            scheme_part, rest = nordvpn_proxy.split("://", 1)
            if "@" in rest:
                auth_part, host_part = rest.rsplit("@", 1)
                pproxy_url = f"{scheme_part}://{host_part}#{auth_part}"
            else:
                pproxy_url = nordvpn_proxy
        else:
            pproxy_url = nordvpn_proxy

        print(f"🔄 Converted URL for pproxy: {pproxy_url}")

        # 啟動 pproxy 轉換器
        cmd = [
            sys.executable, "-m", "pproxy",
            "-l", f"http://0.0.0.0:{http_proxy_port}",
            "-r", pproxy_url,
            "-v"  # 詳細輸出
        ]
        
        print(f"🚀 Starting pproxy converter on port {http_proxy_port}...")
        print(f"Command: {' '.join(cmd)}")
        
        # 啟動 pproxy 進程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待服務啟動
        time.sleep(3)
        
        # 檢查進程是否還在運行
        if process.poll() is None:
            print("✅ pproxy converter started successfully")
            
            # 測試 HTTP 代理連接
            http_proxy_url = f"http://127.0.0.1:{http_proxy_port}"
            print(f"🧪 Testing HTTP proxy: {http_proxy_url}")
            
            # 設置代理環境變量
            os.environ['HTTP_PROXY'] = http_proxy_url
            os.environ['HTTPS_PROXY'] = http_proxy_url
            
            try:
                # 測試連接
                test_url = "https://httpbin.org/ip"
                print(f"📡 Testing connection to: {test_url}")
                
                with urllib.request.urlopen(test_url, timeout=10) as response:
                    result = response.read().decode('utf-8')
                    print(f"✅ HTTP proxy test successful!")
                    print(f"Response: {result[:200]}...")
                    
                    # 停止 pproxy 進程
                    process.terminate()
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()
                    
                    return True
                    
            except Exception as e:
                print(f"❌ HTTP proxy connection test failed: {e}")
                
                # 停止 pproxy 進程
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                
                return False
        else:
            # 進程已經退出，檢查錯誤
            stdout, stderr = process.communicate()
            print(f"❌ pproxy converter failed to start")
            print(f"stdout: {stdout}")
            print(f"stderr: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing pproxy conversion: {e}")
        return False

def test_direct_socks5():
    """測試直接使用 SOCKS5 代理"""
    nordvpn_proxy = os.getenv('NORDVPN_PROXY')
    if not nordvpn_proxy:
        print("❌ NORDVPN_PROXY not found in environment variables")
        return False
    
    print(f"🔗 Testing direct SOCKS5 proxy: {nordvpn_proxy}")
    
    # 設置 SOCKS5 代理環境變量
    os.environ['ALL_PROXY'] = nordvpn_proxy
    os.environ['all_proxy'] = nordvpn_proxy
    
    try:
        # 測試連接
        test_url = "https://httpbin.org/ip"
        print(f"📡 Testing connection to: {test_url}")
        
        with urllib.request.urlopen(test_url, timeout=10) as response:
            result = response.read().decode('utf-8')
            print(f"✅ SOCKS5 proxy test successful!")
            print(f"Response: {result[:200]}...")
            return True
            
    except Exception as e:
        print(f"❌ SOCKS5 proxy connection test failed: {e}")
        return False

def clear_proxy_env():
    """清除代理環境變量"""
    proxy_vars = [
        'HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy',
        'ALL_PROXY', 'all_proxy', 'SOCKS_PROXY', 'socks_proxy'
    ]
    
    for var in proxy_vars:
        if var in os.environ:
            del os.environ[var]
    
    print("🧹 Cleared proxy environment variables")

def main():
    """主測試函數"""
    print("=" * 60)
    print("🧪 pproxy Setup and Functionality Test")
    print("=" * 60)
    
    # 1. 安裝 pproxy
    if not install_pproxy():
        print("❌ Cannot proceed without pproxy")
        return False
    
    print("\n" + "-" * 40)
    
    # 2. 測試直接 SOCKS5 連接
    print("📋 Step 1: Testing direct SOCKS5 proxy")
    clear_proxy_env()
    socks5_success = test_direct_socks5()
    
    print("\n" + "-" * 40)
    
    # 3. 測試 pproxy 轉換
    print("📋 Step 2: Testing pproxy SOCKS5 to HTTP conversion")
    clear_proxy_env()
    http_success = test_pproxy_conversion()
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"  Direct SOCKS5: {'✅ Success' if socks5_success else '❌ Failed'}")
    print(f"  HTTP (via pproxy): {'✅ Success' if http_success else '❌ Failed'}")
    
    if socks5_success and http_success:
        print("\n🎉 All tests passed! pproxy setup is working correctly.")
        print("You can now use marker_ocr_http_proxy.py for HTTP proxy testing.")
        return True
    elif socks5_success and not http_success:
        print("\n⚠️ SOCKS5 works but HTTP conversion failed.")
        print("Check pproxy installation and configuration.")
        return False
    elif not socks5_success and http_success:
        print("\n⚠️ HTTP conversion works but direct SOCKS5 failed.")
        print("This is unusual - check SOCKS5 proxy configuration.")
        return False
    else:
        print("\n❌ Both tests failed. Check proxy configuration.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
