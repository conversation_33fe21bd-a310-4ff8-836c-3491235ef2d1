import asyncio
import os
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

# 導入 Document Intelligence 異步客戶端和必要的模塊
from azure.core.credentials import AzureKeyCredential
from azure.ai.documentintelligence.aio import DocumentIntelligenceClient
from azure.ai.documentintelligence.models import (
    AnalyzeResult,
    DocumentAnalysisFeature,
    DocumentContentFormat
)

# 導入環境變量管理
from dotenv import load_dotenv
load_dotenv()

def disable_proxy_for_azure():
    """為 Azure API 調用禁用代理設置"""
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
    original_proxies = {}

    for var in proxy_vars:
        if var in os.environ:
            original_proxies[var] = os.environ[var]
            del os.environ[var]

    return original_proxies

def restore_proxy_settings(original_proxies):
    """恢復原始代理設置"""
    for var, value in original_proxies.items():
        os.environ[var] = value

def create_azure_config():
    """創建 Azure Document Intelligence 配置"""
    print(f"\n{'='*50}")
    print("配置 Azure Document Intelligence 服務")
    print(f"{'='*50}")
    
    config = {
        "azure_endpoint": os.getenv('AZURE_DOCINTEL_ENDPOINT'),
        "azure_key": os.getenv('AZURE_DOCINTEL_KEY'),
        "model_id": os.getenv('AZURE_MODEL_ID', 'prebuilt-layout'),
        "locale": os.getenv('AZURE_LOCALE', 'en-US'),
        "features": [
            DocumentAnalysisFeature.FORMULAS,
            DocumentAnalysisFeature.OCR_HIGH_RESOLUTION
        ],
        "output_format": DocumentContentFormat.MARKDOWN
    }
    
    # 驗證必要的配置
    if not config["azure_endpoint"] or not config["azure_key"]:
        print("⚠️ 警告: 未設置 Azure Document Intelligence 端點或密鑰")
        print("請在 .env 文件中設置 AZURE_DOCINTEL_ENDPOINT 和 AZURE_DOCINTEL_KEY")
        return None
    
    print(f"✅ Azure 端點: {config['azure_endpoint']}")
    print(f"✅ 模型 ID: {config['model_id']}")
    print(f"✅ 語言區域: {config['locale']}")
    
    return config

async def analyze_single_document(
    client: DocumentIntelligenceClient, 
    pdf_path: str, 
    doc_locale: str,
    model_id: str = "prebuilt-layout",
    features: Optional[List[DocumentAnalysisFeature]] = None
) -> Dict[str, Any]:
    """
    異步分析單個 PDF 文件，並根據文件語言優化配置。
    
    Args:
        client: 異步 DocumentIntelligenceClient 實例。
        pdf_path: PDF 文件的本地路徑。
        doc_locale: 文檔的語言區域，例如 "en-US" 或 "zh-Hant"。
        model_id: 使用的模型 ID。
        features: 分析功能列表。
    
    Returns:
        包含分析結果的字典。
    """
    print(f"🚀 開始分析文件: {os.path.basename(pdf_path)} (語言: {doc_locale})")
    
    if not os.path.exists(pdf_path):
        error_msg = f"❌ 文件不存在: {pdf_path}"
        print(error_msg)
        return {
            'success': False,
            'error': error_msg,
            'file_path': pdf_path,
            'content': None,
            'processing_time': 0
        }
    
    # 設置默認功能
    if features is None:
        features = [
            DocumentAnalysisFeature.FORMULAS,
            DocumentAnalysisFeature.OCR_HIGH_RESOLUTION
        ]
    
    start_time = time.time()
    
    try:
        with open(pdf_path, "rb") as f:
            # 核心：發起分析請求
            # 新增了 locale 參數以提升特定語言的識別準確率
            poller = await client.begin_analyze_document(
                model_id=model_id,
                body=f,
                locale=doc_locale,
                output_content_format=DocumentContentFormat.MARKDOWN
            )
        
        result: AnalyzeResult = await poller.result()
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"✅ 文件分析完成: {os.path.basename(pdf_path)}")
        print(f"⏱️ 處理時間: {processing_time:.2f} 秒")
        
        if result.content:
            print(f"📄 提取內容長度: {len(result.content)} 字符")
            return {
                'success': True,
                'error': None,
                'file_path': pdf_path,
                'content': result.content,
                'processing_time': processing_time,
                'locale': doc_locale,
                'model_id': model_id,
                'content_length': len(result.content)
            }
        else:
            warning_msg = f"⚠️ 文件 {os.path.basename(pdf_path)} 未返回任何內容。"
            print(warning_msg)
            return {
                'success': False,
                'error': warning_msg,
                'file_path': pdf_path,
                'content': None,
                'processing_time': processing_time
            }
            
    except Exception as e:
        end_time = time.time()
        processing_time = end_time - start_time
        error_msg = f"❌ 分析文件時發生錯誤 {os.path.basename(pdf_path)}: {e}"
        print(error_msg)
        return {
            'success': False,
            'error': error_msg,
            'file_path': pdf_path,
            'content': None,
            'processing_time': processing_time
        }

def save_azure_output(result: Dict[str, Any], model_name: str = "Azure Document AI", 
                     base_dir: str = "ocr_comparison_results") -> Optional[Path]:
    """保存 Azure Document AI 的輸出結果"""
    if not result['success'] or not result['content']:
        print(f"⚠️ 無法保存結果：{result.get('error', '未知錯誤')}")
        return None
    
    # 創建模型特定的文件夾
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    model_folder = Path(base_dir) / f"{model_name.replace(' ', '_').replace('/', '_')}_{timestamp}"
    model_folder.mkdir(parents=True, exist_ok=True)
    
    # 保存 markdown 文件
    markdown_file = model_folder / "ocr_result.md"
    with open(markdown_file, 'w', encoding='utf-8') as f:
        f.write(f"# {model_name} OCR 結果\n\n")
        f.write(f"生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # 添加配置信息
        f.write("## 配置參數\n\n")
        f.write(f"- **處理時間**: {result['processing_time']:.2f} 秒\n")
        f.write(f"- **語言區域**: {result.get('locale', 'N/A')}\n")
        f.write(f"- **模型 ID**: {result.get('model_id', 'N/A')}\n")
        f.write(f"- **內容長度**: {result.get('content_length', 0)} 字符\n\n")
        
        f.write("## 提取的文本內容\n\n")
        f.write(result['content'])
    
    print(f"  Azure 模型輸出已保存至: {model_folder}")
    return model_folder

async def test_azure_ocr(pdf_path: str = "sample.pdf", doc_locale: str = "en-US") -> Dict[str, Any]:
    """測試 Azure Document Intelligence OCR 性能"""
    print(f"\n{'='*50}")
    print("開始測試 Azure Document Intelligence")
    print(f"{'='*50}")

    # 獲取配置
    config = create_azure_config()
    if not config:
        return {
            'model_name': 'Azure Document AI',
            'success': False,
            'error': 'Azure 配置不完整',
            'processing_time': 0,
            'text_length': 0,
            'text_content': '',
            'output_folder': None
        }

    # 為 Azure API 調用禁用代理
    print("🔧 為 Azure API 禁用代理設置...")
    original_proxies = disable_proxy_for_azure()

    try:
        # 創建異步客戶端
        async with DocumentIntelligenceClient(
            endpoint=config["azure_endpoint"],
            credential=AzureKeyCredential(config["azure_key"])
        ) as client:

            # 分析文檔
            result = await analyze_single_document(
                client=client,
                pdf_path=pdf_path,
                doc_locale=doc_locale,
                model_id=config["model_id"],
                features=config["features"]
            )
            
            if result['success']:
                # 保存結果
                output_folder = save_azure_output(result, "Azure Document AI")
                
                return {
                    'model_name': 'Azure Document AI',
                    'success': True,
                    'error': None,
                    'processing_time': result['processing_time'],
                    'text_length': len(result['content']),
                    'image_count': 0,  # Azure Document AI 不直接提供圖像提取
                    'text_content': result['content'],
                    'images': {},
                    'output_folder': output_folder,
                    'config': config,
                    'attempts': 1
                }
            else:
                return {
                    'model_name': 'Azure Document AI',
                    'success': False,
                    'error': result['error'],
                    'processing_time': result['processing_time'],
                    'text_length': 0,
                    'image_count': 0,
                    'text_content': '',
                    'images': {},
                    'output_folder': None,
                    'config': config,
                    'attempts': 1
                }

    except Exception as e:
        error_msg = f"Azure Document AI 測試失敗: {e}"
        print(f"❌ {error_msg}")
        return {
            'model_name': 'Azure Document AI',
            'success': False,
            'error': error_msg,
            'processing_time': 0,
            'text_length': 0,
            'image_count': 0,
            'text_content': '',
            'images': {},
            'output_folder': None,
            'config': config if 'config' in locals() else {},
            'attempts': 1
        }
    finally:
        # 恢復原始代理設置
        print("🔧 恢復原始代理設置...")
        restore_proxy_settings(original_proxies)

# 用於批量處理的函數
async def batch_analyze_documents(documents_list: List[tuple]) -> List[Dict[str, Any]]:
    """
    批量分析多個文檔

    Args:
        documents_list: 元組列表，每個元組包含 (文件路徑, 語言區域)

    Returns:
        分析結果列表
    """
    config = create_azure_config()
    if not config:
        return []

    # 為 Azure API 調用禁用代理
    print("🔧 為批量處理禁用代理設置...")
    original_proxies = disable_proxy_for_azure()

    try:
        async with DocumentIntelligenceClient(
            endpoint=config["azure_endpoint"],
            credential=AzureKeyCredential(config["azure_key"])
        ) as client:
            tasks = [
                analyze_single_document(
                    client, pdf_path, locale,
                    config["model_id"], config["features"]
                )
                for pdf_path, locale in documents_list
            ]
            results = await asyncio.gather(*tasks)

        print("\n🎉 所有文件處理完畢。")
        return results
    finally:
        # 恢復原始代理設置
        print("🔧 恢復原始代理設置...")
        restore_proxy_settings(original_proxies)

if __name__ == "__main__":
    # 測試單個文檔
    result = asyncio.run(test_azure_ocr())
    print(f"\n測試結果: {result}")
