#!/usr/bin/env python3
"""
測試 Azure Document Intelligence 整合
Test Azure Document Intelligence integration
"""

import asyncio
import os
from azure_ocr import create_azure_config, test_azure_ocr

def test_config_creation():
    """測試配置創建功能"""
    print("=== 測試 Azure 配置創建 ===")
    
    # 測試沒有環境變量的情況
    original_endpoint = os.environ.get('AZURE_DOCINTEL_ENDPOINT')
    original_key = os.environ.get('AZURE_DOCINTEL_KEY')
    
    # 清除環境變量
    if 'AZURE_DOCINTEL_ENDPOINT' in os.environ:
        del os.environ['AZURE_DOCINTEL_ENDPOINT']
    if 'AZURE_DOCINTEL_KEY' in os.environ:
        del os.environ['AZURE_DOCINTEL_KEY']
    
    config = create_azure_config()
    if config is None:
        print("✅ 正確處理了缺少配置的情況")
    else:
        print("❌ 應該返回 None 當配置缺失時")
    
    # 恢復環境變量
    if original_endpoint:
        os.environ['AZURE_DOCINTEL_ENDPOINT'] = original_endpoint
    if original_key:
        os.environ['AZURE_DOCINTEL_KEY'] = original_key
    
    print("=== 配置測試完成 ===\n")

async def test_azure_ocr_without_credentials():
    """測試 Azure OCR 功能（不使用真實憑證）"""
    print("=== 測試 Azure OCR 功能 ===")
    
    # 測試沒有憑證的情況
    result = await test_azure_ocr("sample.pdf", "en-US")
    
    if not result['success']:
        print("✅ 正確處理了缺少憑證的情況")
        print(f"錯誤信息: {result['error']}")
    else:
        print("❌ 應該失敗當沒有有效憑證時")
    
    print("=== Azure OCR 測試完成 ===\n")

def test_imports():
    """測試所有必要的導入"""
    print("=== 測試導入功能 ===")
    
    try:
        from azure_ocr import (
            create_azure_config,
            test_azure_ocr,
            save_azure_output,
            batch_analyze_documents
        )
        print("✅ Azure OCR 模塊導入成功")
        
        from main import main
        print("✅ Main 模塊導入成功")
        
        from marker_ocr import (
            create_gemini_config,
            create_openrouter_config,
            test_model,
            save_results_to_markdown
        )
        print("✅ Marker OCR 模塊導入成功")
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False
    
    print("=== 導入測試完成 ===\n")
    return True

def main():
    """主測試函數"""
    print("開始 Azure Document Intelligence 整合測試\n")
    
    # 測試導入
    if not test_imports():
        print("導入測試失敗，停止測試")
        return
    
    # 測試配置
    test_config_creation()
    
    # 測試 Azure OCR 功能
    asyncio.run(test_azure_ocr_without_credentials())
    
    print("=== 所有測試完成 ===")
    print("注意：要使用 Azure Document Intelligence，請在 .env 文件中設置：")
    print("- AZURE_DOCINTEL_ENDPOINT=your_azure_endpoint")
    print("- AZURE_DOCINTEL_KEY=your_azure_key")

if __name__ == "__main__":
    main()
