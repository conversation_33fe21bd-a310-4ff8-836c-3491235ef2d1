
import asyncio
from marker_ocr import (
    create_gemini_config,
    create_openrouter_config,
    test_model,
    save_results_to_markdown
)
from azure_ocr import test_azure_ocr

def main():
    """Main function: Execute OCR model comparison test"""
    print("Starting OCR model comparison test")
    print("Test models: Gemini 2.5 Flash vs OpenRouter Deepseek vs Azure Document AI")
    print("Using enhanced proxy management mechanism")

    results = []

    # Test Gemini model
    gemini_config = create_gemini_config()
    gemini_result = test_model(gemini_config, "Gemini 2.5 Flash")
    results.append(gemini_result)

    # Test OpenRouter Deepseek model
    openrouter_config = create_openrouter_config()
    deepseek_result = test_model(openrouter_config, "OpenRouter Deepseek R1")
    results.append(deepseek_result)

    # Test Azure Document AI model
    print(f"\n{'='*50}")
    print("開始測試 Azure Document AI")
    print(f"{'='*50}")
    azure_result = asyncio.run(test_azure_ocr())
    results.append(azure_result)

    # Save results to Markdown file
    report_file = save_results_to_markdown(results)

    print(f"\n{'='*50}")
    print("Test completed!")
    print(f"Detailed comparison report see: {report_file}")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()