#!/usr/bin/env python3
"""
比較 SOCKS5 和 HTTP proxy 在 Gemini API 使用中的性能差異
測試兩種代理類型的兼容性和行為差異
"""

import os
import time
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

# 載入環境變量
load_dotenv()

# 導入我們的測試模塊
from marker_ocr import create_gemini_config, test_model_with_retry
from marker_ocr_http_proxy import create_gemini_config_with_http_proxy, test_model_with_http_proxy_retry
from proxy_manager import ProxyManager

def save_comparison_results(socks5_result, http_result, output_dir="proxy_comparison_results"):
    """保存代理類型比較結果"""
    # 創建輸出目錄
    Path(output_dir).mkdir(exist_ok=True)
    
    # 生成時間戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 創建比較報告
    comparison_file = Path(output_dir) / f"proxy_comparison_{timestamp}.md"
    
    with open(comparison_file, 'w', encoding='utf-8') as f:
        f.write("# Proxy Type Comparison Report\n\n")
        f.write(f"Test time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write("This report compares the performance and compatibility of SOCKS5 vs HTTP proxy for Gemini API access.\n\n")
        
        # 性能比較表格
        f.write("## Performance Comparison\n\n")
        f.write("| Proxy Type | Status | Processing Time(s) | Text Length(chars) | Image Count | Retry Count | Error |\n")
        f.write("|------------|--------|-------------------|-------------------|-------------|-------------|-------|\n")
        
        # SOCKS5 結果
        socks5_status = "✅ Success" if socks5_result['success'] else "❌ Failed"
        socks5_error = socks5_result.get('error', 'None')[:50] + "..." if socks5_result.get('error') and len(socks5_result.get('error', '')) > 50 else socks5_result.get('error', 'None')
        f.write(f"| SOCKS5 | {socks5_status} | {socks5_result['processing_time']:.2f} | {socks5_result['text_length']} | {socks5_result['image_count']} | {socks5_result.get('attempts', 1)} | {socks5_error} |\n")
        
        # HTTP 結果
        http_status = "✅ Success" if http_result['success'] else "❌ Failed"
        http_error = http_result.get('error', 'None')[:50] + "..." if http_result.get('error') and len(http_result.get('error', '')) > 50 else http_result.get('error', 'None')
        f.write(f"| HTTP (via pproxy) | {http_status} | {http_result['processing_time']:.2f} | {http_result['text_length']} | {http_result['image_count']} | {http_result.get('attempts', 1)} | {http_error} |\n")
        
        f.write("\n")
        
        # 詳細分析
        f.write("## Detailed Analysis\n\n")
        
        # SOCKS5 分析
        f.write("### SOCKS5 Proxy Results\n\n")
        if socks5_result['success']:
            f.write(f"- **Status**: ✅ Successful\n")
            f.write(f"- **Processing Time**: {socks5_result['processing_time']:.2f} seconds\n")
            f.write(f"- **Text Extracted**: {socks5_result['text_length']} characters\n")
            f.write(f"- **Images Extracted**: {socks5_result['image_count']}\n")
            f.write(f"- **Retry Attempts**: {socks5_result.get('attempts', 1)}\n")
            if socks5_result.get('output_folder'):
                f.write(f"- **Output Folder**: `{socks5_result['output_folder'].name}`\n")
        else:
            f.write(f"- **Status**: ❌ Failed\n")
            f.write(f"- **Error**: {socks5_result.get('error', 'Unknown error')}\n")
            f.write(f"- **Retry Attempts**: {socks5_result.get('attempts', 1)}\n")
        
        f.write("\n")
        
        # HTTP 分析
        f.write("### HTTP Proxy (via pproxy) Results\n\n")
        if http_result['success']:
            f.write(f"- **Status**: ✅ Successful\n")
            f.write(f"- **Processing Time**: {http_result['processing_time']:.2f} seconds\n")
            f.write(f"- **Text Extracted**: {http_result['text_length']} characters\n")
            f.write(f"- **Images Extracted**: {http_result['image_count']}\n")
            f.write(f"- **Retry Attempts**: {http_result.get('attempts', 1)}\n")
            if http_result.get('output_folder'):
                f.write(f"- **Output Folder**: `{http_result['output_folder'].name}`\n")
        else:
            f.write(f"- **Status**: ❌ Failed\n")
            f.write(f"- **Error**: {http_result.get('error', 'Unknown error')}\n")
            f.write(f"- **Retry Attempts**: {http_result.get('attempts', 1)}\n")
        
        f.write("\n")
        
        # 結論和建議
        f.write("## Conclusions and Recommendations\n\n")
        
        if socks5_result['success'] and http_result['success']:
            # 比較性能
            if socks5_result['processing_time'] < http_result['processing_time']:
                faster_proxy = "SOCKS5"
                time_diff = http_result['processing_time'] - socks5_result['processing_time']
            else:
                faster_proxy = "HTTP"
                time_diff = socks5_result['processing_time'] - http_result['processing_time']
            
            f.write(f"✅ **Both proxy types work successfully with Gemini API**\n\n")
            f.write(f"- **Performance**: {faster_proxy} proxy is faster by {time_diff:.2f} seconds\n")
            f.write(f"- **Reliability**: Both proxies completed successfully\n")
            f.write(f"- **Compatibility**: Both proxy types are compatible with Gemini API\n\n")
            f.write("**Recommendation**: Both proxy types are viable. Choose based on your infrastructure preferences.\n")
            
        elif socks5_result['success'] and not http_result['success']:
            f.write(f"⚠️ **SOCKS5 works, HTTP proxy failed**\n\n")
            f.write(f"- SOCKS5 proxy successfully accessed Gemini API\n")
            f.write(f"- HTTP proxy (via pproxy conversion) failed\n")
            f.write(f"- This may indicate issues with the pproxy conversion process\n\n")
            f.write("**Recommendation**: Use SOCKS5 proxy directly for reliable access.\n")
            
        elif not socks5_result['success'] and http_result['success']:
            f.write(f"⚠️ **HTTP proxy works, SOCKS5 failed**\n\n")
            f.write(f"- HTTP proxy (via pproxy conversion) successfully accessed Gemini API\n")
            f.write(f"- Direct SOCKS5 proxy failed\n")
            f.write(f"- This is unusual and may indicate SOCKS5 configuration issues\n\n")
            f.write("**Recommendation**: Use HTTP proxy via pproxy conversion.\n")
            
        else:
            f.write(f"❌ **Both proxy types failed**\n\n")
            f.write(f"- Neither SOCKS5 nor HTTP proxy could access Gemini API\n")
            f.write(f"- This may indicate broader connectivity or authentication issues\n")
            f.write(f"- Check proxy credentials and network connectivity\n\n")
            f.write("**Recommendation**: Verify proxy configuration and network settings.\n")
        
        # 技術細節
        f.write("\n## Technical Details\n\n")
        f.write("### Test Configuration\n\n")
        f.write("- **SOCKS5 Proxy**: Direct connection using urllib with SOCKS5 proxy\n")
        f.write("- **HTTP Proxy**: SOCKS5 converted to HTTP using pproxy library\n")
        f.write("- **Test Model**: Gemini 2.5 Flash\n")
        f.write("- **Test Document**: sample.pdf\n")
        f.write("- **Max Retries**: 3 attempts per proxy type\n\n")
        
        f.write("### Environment Variables Used\n\n")
        f.write("```\n")
        f.write(f"NORDVPN_PROXY={os.getenv('NORDVPN_PROXY', 'Not set')}\n")
        f.write(f"GEMINI_API_KEY={'Set' if os.getenv('GEMINI_API_KEY') else 'Not set'}\n")
        f.write(f"GEMINI_MODEL={os.getenv('GEMINI_MODEL', 'Not set')}\n")
        f.write("```\n")
    
    print(f"\n📊 Comparison report saved to: {comparison_file}")
    return comparison_file

def main():
    """主比較測試函數"""
    print("=" * 60)
    print("🔄 Proxy Type Comparison Test")
    print("=" * 60)
    print("Testing SOCKS5 vs HTTP proxy performance with Gemini API")
    print()
    
    # 檢查必要的環境變量
    if not os.getenv('NORDVPN_PROXY'):
        print("❌ NORDVPN_PROXY not found in environment variables")
        return False
    
    if not os.getenv('GEMINI_API_KEY'):
        print("❌ GEMINI_API_KEY not found in environment variables")
        return False
    
    # 創建代理管理器
    proxy_manager = ProxyManager()
    
    try:
        # 測試 1: SOCKS5 代理
        print("🧪 Test 1: SOCKS5 Proxy")
        print("-" * 30)
        
        socks5_config = create_gemini_config()
        socks5_result = test_model_with_retry(
            socks5_config, 
            "Gemini 2.5 Flash (SOCKS5)", 
            pdf_path="sample.pdf"
        )
        
        if socks5_result['success']:
            print(f"✅ SOCKS5 test completed successfully in {socks5_result['processing_time']:.2f}s")
        else:
            print(f"❌ SOCKS5 test failed: {socks5_result.get('error', 'Unknown error')}")
        
        print()
        
        # 測試 2: HTTP 代理 (通過 pproxy 轉換)
        print("🧪 Test 2: HTTP Proxy (via pproxy)")
        print("-" * 30)
        
        http_config = create_gemini_config_with_http_proxy()
        http_result = test_model_with_http_proxy_retry(
            http_config, 
            "Gemini 2.5 Flash (HTTP)", 
            pdf_path="sample.pdf"
        )
        
        if http_result['success']:
            print(f"✅ HTTP test completed successfully in {http_result['processing_time']:.2f}s")
        else:
            print(f"❌ HTTP test failed: {http_result.get('error', 'Unknown error')}")
        
        # 保存比較結果
        comparison_file = save_comparison_results(socks5_result, http_result)
        
        # 總結
        print("\n" + "=" * 60)
        print("📊 Test Summary:")
        print(f"  SOCKS5: {'✅ Success' if socks5_result['success'] else '❌ Failed'}")
        print(f"  HTTP:   {'✅ Success' if http_result['success'] else '❌ Failed'}")
        
        if socks5_result['success'] and http_result['success']:
            time_diff = abs(socks5_result['processing_time'] - http_result['processing_time'])
            faster = "SOCKS5" if socks5_result['processing_time'] < http_result['processing_time'] else "HTTP"
            print(f"  Performance: {faster} is faster by {time_diff:.2f}s")
        
        print(f"  Report: {comparison_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Comparison test failed: {e}")
        return False
    
    finally:
        # 清理代理設置
        proxy_manager.clear_proxy()
        print("\n🧹 Proxy settings cleared")

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Proxy comparison test completed successfully!")
    else:
        print("\n❌ Proxy comparison test failed!")
