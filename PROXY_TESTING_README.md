# Proxy Type Testing Guide

本指南說明如何使用 pproxy 庫測試 SOCKS5 和 HTTP proxy 在 Gemini API 中的兼容性和性能差異。

## 概述

我們實現了以下功能：
1. **SOCKS5 到 HTTP 代理轉換**：使用 pproxy 庫將 SOCKS5 代理轉換為 HTTP 代理
2. **代理類型比較測試**：同時測試兩種代理類型的性能和兼容性
3. **增強的錯誤處理**：包含重試機制和詳細的錯誤報告

## 文件結構

```
├── proxy_manager.py              # 增強的代理管理器（支持 pproxy 轉換）
├── marker_ocr.py                 # 原始 SOCKS5 代理測試
├── marker_ocr_http_proxy.py      # HTTP 代理測試（通過 pproxy 轉換）
├── test_pproxy_setup.py          # pproxy 安裝和基本功能測試
├── compare_proxy_types.py        # 代理類型性能比較測試
└── PROXY_TESTING_README.md       # 本文件
```

## 環境設置

確保您的 `.env` 文件包含以下變量：

```bash
# 代理設置
NORDVPN_PROXY=socks5://username:password@host:port

# Gemini API 設置
GEMINI_API_KEY=your_gemini_api_key
GEMINI_MODEL=gemini-2.5-flash
MARKER_GEMINI_SERIVES=gemini

# 其他設置
USE_LLM=true
MARKER_BATCH_MULTIPLIER=1
MARKER_LANG=en
MARKER_FORMAT_LINES=true
MARKER_OUTPUT_FORMAT=markdown
MARKER_FORCE_OCR=false
```

## 使用步驟

### 1. 測試 pproxy 基本功能

首先運行基本功能測試，確保 pproxy 安裝和配置正確：

```bash
uv run test_pproxy_setup.py
```

這個測試會：
- 自動安裝 pproxy 庫（如果未安裝）
- 測試直接 SOCKS5 連接
- 測試 SOCKS5 到 HTTP 的轉換
- 驗證兩種代理類型的基本連接性

### 2. 單獨測試 HTTP 代理

測試使用 HTTP 代理（通過 pproxy 轉換）的 OCR 功能：

```bash
uv run marker_ocr_http_proxy.py
```

這會：
- 將 SOCKS5 代理轉換為 HTTP 代理
- 使用 HTTP 代理執行 Gemini OCR
- 將結果保存到 `ocr_http_proxy_results/` 目錄

### 3. 比較兩種代理類型

運行完整的比較測試：

```bash
uv run compare_proxy_types.py
```

這會：
- 同時測試 SOCKS5 和 HTTP 代理
- 比較性能和兼容性
- 生成詳細的比較報告

## 輸出結果

### 測試結果目錄

- `ocr_comparison_results/`：SOCKS5 代理測試結果
- `ocr_http_proxy_results/`：HTTP 代理測試結果  
- `proxy_comparison_results/`：代理類型比較報告

### 比較報告內容

比較報告包含：
- **性能比較表格**：處理時間、文本長度、圖片數量等
- **詳細分析**：每種代理類型的成功/失敗狀態
- **結論和建議**：基於測試結果的使用建議
- **技術細節**：測試配置和環境變量

## 代理管理器增強功能

### 新增方法

1. **`set_global_proxy_with_conversion()`**
   - 自動檢測 SOCKS5 代理並轉換為 HTTP
   - 保留原有的 `set_global_proxy()` 方法以保持兼容性

2. **`_start_socks5_to_http_converter()`**
   - 啟動 pproxy 轉換器進程
   - 自動安裝 pproxy 庫（如果需要）
   - 返回本地 HTTP 代理 URL

3. **`_stop_proxy_converter()`**
   - 停止 pproxy 轉換器進程
   - 清理資源

### 使用示例

```python
from proxy_manager import ProxyManager

proxy_manager = ProxyManager()

# 使用 SOCKS5 到 HTTP 轉換
proxy_manager.set_global_proxy_with_conversion('socks5://user:pass@host:port')

# 清理（會自動停止轉換器）
proxy_manager.clear_proxy()
```

## 故障排除

### 常見問題

1. **pproxy 安裝失敗**
   ```bash
   pip install pproxy
   ```

2. **端口衝突**
   - 默認使用端口 8888 作為本地 HTTP 代理
   - 如果衝突，修改 `ProxyManager` 中的 `http_proxy_port`

3. **代理連接失敗**
   - 檢查 NORDVPN_PROXY 環境變量
   - 驗證代理憑證和網絡連接
   - 查看終端輸出中的錯誤信息

4. **Gemini API 地理位置錯誤**
   - 確保代理正確設置
   - 檢查 'User location is not supported for the API use' 錯誤

### 調試技巧

1. **查看詳細日誌**
   - 所有測試腳本都包含詳細的日誌輸出
   - 注意 ✅ 成功和 ❌ 失敗的標記

2. **檢查代理狀態**
   ```python
   proxy_manager.verify_proxy_settings()
   ```

3. **測試基本連接**
   ```python
   proxy_manager.test_proxy_connection()
   ```

## 預期結果

成功的測試應該顯示：
- ✅ pproxy 安裝和配置正確
- ✅ SOCKS5 代理連接成功
- ✅ HTTP 代理轉換成功
- ✅ Gemini API 通過兩種代理類型都能正常工作
- 📊 性能比較報告顯示兩種代理的差異

## 技術原理

### SOCKS5 到 HTTP 轉換

1. **pproxy 啟動**：在本地端口（默認 8888）啟動 HTTP 代理服務器
2. **流量轉發**：HTTP 請求通過 pproxy 轉發到 SOCKS5 代理
3. **透明代理**：應用程序使用標準 HTTP 代理設置，無需修改代碼

### 代理類型比較

- **SOCKS5**：直接使用 urllib 的 SOCKS5 支持
- **HTTP**：通過 pproxy 轉換，使用標準 HTTP 代理協議
- **性能**：HTTP 可能有輕微的轉換開銷，但通常差異很小
- **兼容性**：某些應用可能對不同代理類型有不同的支持程度

這個測試框架幫助您確定哪種代理類型最適合您的 Gemini API 使用場景。
